# University Programs Web Crawler

A Python web crawler designed to extract academic program information from university websites and save the data to JSON and CSV files.

## Features

- **Generic crawler**: Works with most university websites using common HTML patterns
- **Respectful crawling**: Includes delays between requests to avoid overwhelming servers
- **Multiple output formats**: Saves data in both JSON and CSV formats
- **Customizable**: Easy to modify for specific university website structures
- **Comprehensive data extraction**: Captures program titles, descriptions, degree types, URLs, and metadata

## Installation

1. Install Python 3.7 or higher
2. Install required dependencies:

```bash
pip install -r requirements.txt
```

## Quick Start

### Command Line Usage

Basic usage with a university URL:

```bash
python university_programs_crawler.py --url https://university.edu
```

Specify specific pages to crawl:

```bash
python university_programs_crawler.py --url https://university.edu --pages https://university.edu/programs https://university.edu/academics
```

Customize output format and filename:

```bash
python university_programs_crawler.py --url https://university.edu --output my_university --format json
```

### Python Script Usage

```python
from university_programs_crawler import UniversityProgramsCrawler

# Initialize crawler
crawler = UniversityProgramsCrawler("https://university.edu", delay=1.0)

# Crawl specific pages
pages = [
    "https://university.edu/programs",
    "https://university.edu/academics"
]

programs = crawler.crawl_university(pages)

# Save results
crawler.save_to_json(programs, "programs.json")
crawler.save_to_csv(programs, "programs.csv")
```

## Command Line Options

- `--url`: Base URL of the university (required)
- `--pages`: Specific program pages to crawl (optional)
- `--output`: Output filename without extension (default: "university_programs")
- `--format`: Output format - "json", "csv", or "both" (default: "both")
- `--delay`: Delay between requests in seconds (default: 1.0)

## Output Format

The crawler extracts the following information for each program:

- **title**: Program name
- **description**: Program description (truncated to 500 characters)
- **degree_type**: Type of degree (Bachelor, Master, PhD, etc.)
- **url**: Direct link to the program page
- **source_url**: URL where the program was found
- **scraped_at**: Timestamp when the data was collected

### Example JSON Output

```json
[
  {
    "title": "Computer Science Bachelor's Degree",
    "description": "A comprehensive program covering algorithms, data structures, software engineering...",
    "degree_type": "Bachelor",
    "url": "https://university.edu/programs/computer-science-bs",
    "source_url": "https://university.edu/programs",
    "scraped_at": "2024-01-15 14:30:22"
  }
]
```

## Customization

### For Specific Universities

The crawler uses generic selectors that work with most university websites. However, you can customize it for specific universities by:

1. Modifying the `extract_programs_generic` method
2. Adding university-specific selectors
3. Customizing the `extract_program_info` method

### Example Customization

```python
def extract_programs_custom_university(self, soup, url):
    """Custom extraction for a specific university."""
    programs = []
    
    # University-specific selectors
    program_elements = soup.select('.custom-program-class')
    
    for element in program_elements:
        # Custom extraction logic
        title = element.select_one('.custom-title-class').get_text(strip=True)
        description = element.select_one('.custom-desc-class').get_text(strip=True)
        
        programs.append({
            'title': title,
            'description': description,
            # ... other fields
        })
    
    return programs
```

## Best Practices

1. **Be respectful**: Use appropriate delays between requests (1-2 seconds minimum)
2. **Check robots.txt**: Ensure you're allowed to crawl the target website
3. **Handle errors gracefully**: The crawler includes error handling, but monitor for issues
4. **Test with small samples**: Start with a few pages before crawling entire sites
5. **Verify data quality**: Review the extracted data for accuracy

## Troubleshooting

### No Programs Found

If the crawler doesn't find any programs:

1. Check if the university website uses JavaScript to load content (this crawler only works with static HTML)
2. Inspect the website's HTML structure and customize the selectors
3. Verify the URLs are accessible and contain program information
4. Check the console output for error messages

### Incomplete Data

If some fields are missing:

1. The website might use different HTML structures
2. Customize the extraction methods for better accuracy
3. Add more specific selectors for the target website

## Legal and Ethical Considerations

- Always check the website's `robots.txt` file
- Respect rate limits and use appropriate delays
- Don't overload servers with too many concurrent requests
- Consider reaching out to the university for official data if available
- Use the data responsibly and in accordance with the website's terms of service

## Contributing

Feel free to submit issues and enhancement requests. Contributions are welcome!

## License

This project is open source. Please use responsibly and ethically.
