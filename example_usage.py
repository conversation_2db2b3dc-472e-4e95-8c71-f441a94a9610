#!/usr/bin/env python3
"""
Example usage of the University Programs Crawler

This script demonstrates how to use the crawler with specific universities.
"""

from university_programs_crawler import UniversityProgramsCrawler
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)

def crawl_mit_programs():
    """Example: Crawl MIT programs."""
    print("Crawling MIT programs...")
    
    crawler = UniversityProgramsCrawler("https://web.mit.edu", delay=1.5)
    
    # MIT specific pages
    mit_pages = [
        "https://web.mit.edu/catalog/degre.html",
        "https://web.mit.edu/academics/",
    ]
    
    programs = crawler.crawl_university(mit_pages)
    
    if programs:
        crawler.save_to_json(programs, "mit_programs.json")
        crawler.save_to_csv(programs, "mit_programs.csv")
        print(f"Found {len(programs)} MIT programs")
    else:
        print("No MIT programs found")

def crawl_stanford_programs():
    """Example: Crawl Stanford programs."""
    print("Crawling Stanford programs...")
    
    crawler = UniversityProgramsCrawler("https://www.stanford.edu", delay=1.5)
    
    # Stanford specific pages
    stanford_pages = [
        "https://www.stanford.edu/academics/",
        "https://exploredegrees.stanford.edu/",
    ]
    
    programs = crawler.crawl_university(stanford_pages)
    
    if programs:
        crawler.save_to_json(programs, "stanford_programs.json")
        crawler.save_to_csv(programs, "stanford_programs.csv")
        print(f"Found {len(programs)} Stanford programs")
    else:
        print("No Stanford programs found")

def crawl_custom_university(university_url, output_name):
    """Crawl a custom university."""
    print(f"Crawling {university_url}...")
    
    crawler = UniversityProgramsCrawler(university_url, delay=1.0)
    
    # Try common program page patterns
    pages = [
        university_url,
        f"{university_url}/programs",
        f"{university_url}/academics",
        f"{university_url}/degrees",
        f"{university_url}/courses",
        f"{university_url}/study",
    ]
    
    programs = crawler.crawl_university(pages)
    
    if programs:
        crawler.save_to_json(programs, f"{output_name}_programs.json")
        crawler.save_to_csv(programs, f"{output_name}_programs.csv")
        print(f"Found {len(programs)} programs from {university_url}")
        
        # Print first few programs as preview
        print("\nFirst few programs found:")
        for i, program in enumerate(programs[:3]):
            print(f"{i+1}. {program['title']}")
            if program['degree_type']:
                print(f"   Degree: {program['degree_type']}")
            if program['description']:
                print(f"   Description: {program['description'][:100]}...")
            print()
    else:
        print(f"No programs found from {university_url}")
        print("You may need to customize the crawler for this specific website.")

if __name__ == "__main__":
    # Example usage
    print("University Programs Crawler - Example Usage\n")
    
    # Uncomment the university you want to crawl:
    
    # crawl_mit_programs()
    # crawl_stanford_programs()
    
    # Or crawl a custom university:
    # crawl_custom_university("https://www.harvard.edu", "harvard")
    # crawl_custom_university("https://www.berkeley.edu", "berkeley")
    
    print("To use this script:")
    print("1. Uncomment one of the example functions above")
    print("2. Or use the command line interface:")
    print("   python university_programs_crawler.py --url https://university.edu --output university_programs")
    print("\nFor more options, run:")
    print("   python university_programs_crawler.py --help")
